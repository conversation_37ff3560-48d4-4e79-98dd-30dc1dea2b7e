<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\StudentClass;
use App\Models\QuestionMaterial;
use App\Models\User;
use App\Models\Question;
use App\Models\Exam;
use App\Models\ExamDetail;
use App\Models\ExamResult;
use App\Models\ExamTemporaryAnswer;
use App\Models\Student;
use App\Models\Answer;
use Illuminate\Support\Facades\Log;


class ScheduleExamController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();

        // Role-based filtering: teachers see only their own exams, admins see all exams
        // Order by newest first (created_at desc)
        if ($user->role === 'teacher') {
            $exams = Exam::with(['questionmaterial', 'class', 'teacher'])
                ->where('teacher_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->get();
        } else {
            // Ad<PERSON> can see all exams
            $exams = Exam::with(['questionmaterial', 'class', 'teacher'])
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('admin.exam.schedule.index', [
            'title' => 'Jadwal Ujian',
            'exam' => $exams,
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();
        $classStudent = StudentClass::select('id', 'name')->orderBy('name', 'asc')->get();
        $questionMaterial = QuestionMaterial::select('id', 'name')->orderBy('name', 'asc')->get();
        $questions = Question::orderBy('id', 'asc')->get()->groupBy('type');

        // If admin, get list of teachers
        $teachers = [];
        if ($user->role === 'admin') {
            $teachers = User::where('role', 'teacher')->select('id', 'name')->orderBy('name', 'asc')->get();
        }

        return view('admin.exam.schedule.add', [
            'title' => 'Tambah Jadwal Ujian',
            'classStudent' => $classStudent,
            'questionMaterial' => $questionMaterial,
            'questions' => $questions,
            'teachers' => $teachers,
            'isAdmin' => $user->role === 'admin',
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();

        try {
            $validationRules = [
                'name' => 'required',
                'kkm' => 'required|integer',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer',
                'question_ids' => 'required|array',
                'question_ids.*' => 'exists:question,id',
                'show_score' => 'nullable|boolean',
                'schedule_exam' => 'nullable|boolean',
            ];

            // Conditional validation based on schedule_exam checkbox
            // Check for checkbox value (HTML checkboxes send "1" when checked, nothing when unchecked)
            $scheduleExamChecked = $request->has('schedule_exam') && !empty($request->schedule_exam);

            if ($scheduleExamChecked) {
                // If schedule is set, require date/time fields
                $validationRules['startdate'] = 'required|date';
                $validationRules['enddate'] = 'required|date|after_or_equal:startdate';
                $validationRules['starttime'] = 'required';
                $validationRules['endtime'] = 'required';
                $validationRules['duration'] = 'required|integer|min:1';
            } else {
                // If schedule is not set, require manual duration and make date/time fields nullable
                $validationRules['manual_duration'] = 'required|integer|min:1';
                $validationRules['startdate'] = 'nullable|date';
                $validationRules['enddate'] = 'nullable|date';
                $validationRules['starttime'] = 'nullable';
                $validationRules['endtime'] = 'nullable';
                $validationRules['duration'] = 'nullable|integer';
            }

            // Add teacher_id validation for admin users
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
            }

            $validationMessages = [
                'name.required' => 'Nama ujian wajib diisi',
                'startdate.required' => 'Tanggal mulai wajib diisi',
                'startdate.date' => 'Format tanggal mulai tidak valid',
                'enddate.required' => 'Tanggal selesai wajib diisi',
                'enddate.date' => 'Format tanggal selesai tidak valid',
                'enddate.after_or_equal' => 'Tanggal selesai harus sama atau setelah tanggal mulai',
                'starttime.required' => 'Waktu mulai wajib diisi',
                'endtime.required' => 'Waktu selesai wajib diisi',
                'duration.required' => 'Durasi wajib diisi',
                'duration.integer' => 'Durasi harus berupa angka',
                'duration.min' => 'Durasi minimal 1 menit',
                'manual_duration.required' => 'Durasi ujian wajib diisi',
                'manual_duration.integer' => 'Durasi harus berupa angka',
                'manual_duration.min' => 'Durasi minimal 1 menit',
                'kkm.required' => 'KKM wajib diisi',
                'kkm.integer' => 'KKM harus berupa angka',
                'classid.required' => 'Kelas wajib dipilih',
                'classid.exists' => 'Kelas yang dipilih tidak valid',
                'questionmaterialid.required' => 'Bank soal wajib dipilih',
                'questionmaterialid.exists' => 'Bank soal yang dipilih tidak valid',
                'trials.required' => 'Jumlah percobaan wajib diisi',
                'trials.integer' => 'Jumlah percobaan harus berupa angka',
                'question_ids.required' => 'Minimal pilih satu soal',
                'question_ids.array' => 'Format soal tidak valid',
                'question_ids.*.exists' => 'Ada soal yang dipilih tidak valid',
            ];

            // Add teacher_id validation messages for admin users
            if ($user->role === 'admin') {
                $validationMessages['teacher_id.required'] = 'Pilih guru untuk ujian ini';
                $validationMessages['teacher_id.exists'] = 'Guru yang dipilih tidak valid';
            }

            $request->validate($validationRules, $validationMessages);

            // Handle different scenarios based on schedule_exam checkbox
            $duration = 0;
            $startdate = null;
            $enddate = null;
            $starttime = null;
            $endtime = null;

            if ($scheduleExamChecked) {
                // Schedule is set - use provided dates/times and calculate duration
                $startdate = $request->startdate;
                $enddate = $request->enddate;
                $starttime = $request->starttime;
                $endtime = $request->endtime;

                // Additional validation to ensure all date/time fields are properly filled
                if (empty($startdate) || empty($enddate) || empty($starttime) || empty($endtime)) {
                    return back()->withErrors(['schedule_exam' => 'Semua field tanggal dan waktu harus diisi ketika jadwal ujian ditentukan'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
                }

                // Cek manual tambahan (optional karena sudah divalidasi sebelumnya)
                if ($startdate === $enddate && $starttime > $endtime) {
                    return back()->withErrors(['starttime' => 'Waktu mulai tidak boleh lebih dari waktu akhir'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
                }

                // Validasi konflik jadwal - WAJIB untuk ujian terjadwal
                if (Exam::hasScheduleConflict(
                    $request->classid,
                    $startdate,
                    $enddate,
                    $starttime,
                    $endtime
                )) {
                    $conflictingExams = Exam::getConflictingExams(
                        $request->classid,
                        $startdate,
                        $enddate,
                        $starttime,
                        $endtime
                    );

                    $conflictMessage = 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini:';
                    foreach ($conflictingExams as $exam) {
                        $conflictMessage .= "\n- {$exam->name} ({$exam->startdate} {$exam->starttime} - {$exam->enddate} {$exam->endtime})";
                    }

                    return back()->withErrors(['schedule_conflict' => $conflictMessage])->withInput()->with('error', 'Validasi gagal. Jadwal ujian bentrok dengan ujian yang sudah ada.');
                }

                // Calculate duration automatically
                try {
                    $startDateTime = new \DateTime($startdate . ' ' . $starttime);
                    $endDateTime = new \DateTime($enddate . ' ' . $endtime);
                    $interval = $startDateTime->diff($endDateTime);
                    $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

                    // Validate that end time is after start time
                    if ($duration <= 0) {
                        return back()->withErrors(['endtime' => 'Waktu selesai harus setelah waktu mulai'])->withInput();
                    }
                } catch (\Exception $e) {
                    return back()->withErrors(['schedule_exam' => 'Format tanggal atau waktu tidak valid'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali format tanggal dan waktu.');
                }
            } else {
                // Manual duration - use provided duration, set dates/times to null
                $duration = $request->manual_duration;
                $startdate = null;
                $enddate = null;
                $starttime = null;
                $endtime = null;
            }



            // Simpan data ujian
            $exam = Exam::create([
                'name' => $request->name,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'starttime' => $starttime,
                'endtime' => $endtime,
                'duration' => $duration,
                'token' => $this->generateToken(),
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $user->role === 'admin' ? $request->teacher_id : $user->id,
                'show_score' => $request->has('show_score') ? true : false,
            ]);

            // Simpan detail soal yang dipilih
            foreach ($request->question_ids as $soalId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $soalId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            return redirect()->route('exam.index')->with('success', 'Jadwal ujian berhasil ditambahkan');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
        } catch (\Exception $e) {
            return redirect()->route('exam.index')->with('error', 'Gagal menambahkan jadwal ujian: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();
        $exam = Exam::with(['examDetails', 'examDetails.question.answers'])->findOrFail($id);

        // Role-based access control: teachers can only edit their own exams
        if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
            return redirect()->route('exam.index')->with('error', 'Anda tidak memiliki akses untuk mengedit ujian ini.');
        }
        $selectedQuestionIds = $exam->examDetails->pluck('questionid')->toArray();

        $classStudent = StudentClass::select('id', 'name')->orderBy('name', 'asc')->get();
        $questionMaterial = QuestionMaterial::select('id', 'name')->orderBy('name', 'asc')->get();
        $questions = Question::orderBy('id', 'asc')->get()->groupBy('type');

        // If admin, get list of teachers
        $teachers = [];
        if ($user->role === 'admin') {
            $teachers = User::where('role', 'teacher')->select('id', 'name')->orderBy('name', 'asc')->get();
        }

        return view('admin.exam.schedule.edit', [
            'title' => 'Edit Jadwal Ujian',
            'exam' => $exam,
            'classStudent' => $classStudent,
            'questionMaterial' => $questionMaterial,
            'questions' => $questions,
            'selectedQuestionIds' => $selectedQuestionIds,
            'teachers' => $teachers,
            'isAdmin' => $user->role === 'admin',
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();

        try {
            $validationRules = [
                'name' => 'required',
                'kkm' => 'required|integer',
                'classid' => 'required|exists:class,id',
                'questionmaterialid' => 'required|exists:questionmaterial,id',
                'trials' => 'required|integer',
                'question_ids' => 'required|array',
                'question_ids.*' => 'exists:question,id',
                'show_score' => 'nullable|boolean',
                'schedule_exam' => 'nullable|boolean',
            ];

            // Conditional validation based on schedule_exam checkbox
            // Check for checkbox value (HTML checkboxes send "1" when checked, nothing when unchecked)
            $scheduleExamChecked = $request->has('schedule_exam') && !empty($request->schedule_exam);

            if ($scheduleExamChecked) {
                // If schedule is set, require date/time fields
                $validationRules['startdate'] = 'required|date';
                $validationRules['enddate'] = 'required|date|after_or_equal:startdate';
                $validationRules['starttime'] = 'required';
                $validationRules['endtime'] = 'required';
                $validationRules['duration'] = 'required|integer|min:1';
            } else {
                // If schedule is not set, require manual duration and make date/time fields nullable
                $validationRules['manual_duration'] = 'required|integer|min:1';
                $validationRules['startdate'] = 'nullable|date';
                $validationRules['enddate'] = 'nullable|date';
                $validationRules['starttime'] = 'nullable';
                $validationRules['endtime'] = 'nullable';
                $validationRules['duration'] = 'nullable|integer';
            }

            // Add teacher_id validation for admin users
            if ($user->role === 'admin') {
                $validationRules['teacher_id'] = 'required|exists:users,id';
            }

            $validationMessages = [
                'name.required' => 'Nama ujian wajib diisi',
                'startdate.required' => 'Tanggal mulai wajib diisi',
                'startdate.date' => 'Format tanggal mulai tidak valid',
                'enddate.required' => 'Tanggal selesai wajib diisi',
                'enddate.date' => 'Format tanggal selesai tidak valid',
                'enddate.after_or_equal' => 'Tanggal selesai harus sama atau setelah tanggal mulai',
                'starttime.required' => 'Waktu mulai wajib diisi',
                'endtime.required' => 'Waktu selesai wajib diisi',
                'duration.required' => 'Durasi ujian wajib diisi',
                'duration.integer' => 'Durasi ujian harus berupa angka',
                'manual_duration.required' => 'Durasi ujian manual wajib diisi',
                'manual_duration.integer' => 'Durasi ujian manual harus berupa angka',
                'kkm.required' => 'KKM wajib diisi',
                'kkm.integer' => 'KKM harus berupa angka',
                'classid.required' => 'Kelas wajib dipilih',
                'classid.exists' => 'Kelas yang dipilih tidak valid',
                'questionmaterialid.required' => 'Bank soal wajib dipilih',
                'questionmaterialid.exists' => 'Bank soal yang dipilih tidak valid',
                'trials.required' => 'Jumlah percobaan wajib diisi',
                'trials.integer' => 'Jumlah percobaan harus berupa angka',
                'question_ids.required' => 'Minimal pilih satu soal',
                'question_ids.array' => 'Format soal tidak valid',
                'question_ids.*.exists' => 'Ada soal yang dipilih tidak valid',
            ];

            // Add teacher_id validation messages for admin users
            if ($user->role === 'admin') {
                $validationMessages['teacher_id.required'] = 'Pilih guru untuk ujian ini';
                $validationMessages['teacher_id.exists'] = 'Guru yang dipilih tidak valid';
            }

            $request->validate($validationRules, $validationMessages);

            $exam = Exam::findOrFail($id);

            // Role-based access control: teachers can only edit their own exams
            $user = Auth::user();
            if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
                return redirect()->route('exam.index')->with('error', 'Anda tidak memiliki akses untuk mengedit ujian ini.');
            }

            // Handle different scenarios based on schedule_exam checkbox
            $duration = 0;
            $startdate = null;
            $enddate = null;
            $starttime = null;
            $endtime = null;

            if ($scheduleExamChecked) {
                // Schedule is set - use provided dates/times and calculate duration
                $startdate = $request->startdate;
                $enddate = $request->enddate;
                $starttime = $request->starttime;
                $endtime = $request->endtime;

                // Additional validation to ensure all date/time fields are properly filled
                if (empty($startdate) || empty($enddate) || empty($starttime) || empty($endtime)) {
                    return back()->withErrors(['schedule_exam' => 'Semua field tanggal dan waktu harus diisi ketika jadwal ujian ditentukan'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
                }

                // Cek manual tambahan (optional karena sudah divalidasi sebelumnya)
                if ($startdate === $enddate && $starttime > $endtime) {
                    return back()->withErrors(['starttime' => 'Waktu mulai tidak boleh lebih dari waktu akhir'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
                }

                // Validasi konflik jadwal (kecualikan ujian yang sedang diedit)
                if (Exam::hasScheduleConflict(
                    $request->classid,
                    $startdate,
                    $enddate,
                    $starttime,
                    $endtime,
                    $id // Exclude current exam
                )) {
                    $conflictingExams = Exam::getConflictingExams(
                        $request->classid,
                        $startdate,
                        $enddate,
                        $starttime,
                        $endtime,
                        $id
                    );

                    $conflictMessage = 'Jadwal ujian bentrok dengan ujian yang sudah ada untuk kelas ini:';
                    foreach ($conflictingExams as $conflictExam) {
                        $conflictMessage .= "\n- {$conflictExam->name} ({$conflictExam->startdate} {$conflictExam->starttime} - {$conflictExam->enddate} {$conflictExam->endtime})";
                    }

                    return back()->withErrors(['schedule_conflict' => $conflictMessage])->withInput()->with('error', 'Validasi gagal. Jadwal ujian bentrok dengan ujian yang sudah ada.');
                }

                // Calculate duration automatically
                try {
                    $startDateTime = new \DateTime($startdate . ' ' . $starttime);
                    $endDateTime = new \DateTime($enddate . ' ' . $endtime);
                    $interval = $startDateTime->diff($endDateTime);
                    $duration = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

                    // Validate that end time is after start time
                    if ($duration <= 0) {
                        return back()->withErrors(['endtime' => 'Waktu selesai harus setelah waktu mulai'])->withInput();
                    }
                } catch (\Exception $e) {
                    return back()->withErrors(['schedule_exam' => 'Format tanggal atau waktu tidak valid'])->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali format tanggal dan waktu.');
                }
            } else {
                // Manual duration - use provided duration, set dates/times to null
                $duration = $request->manual_duration;
            }



            // Update exam
            $exam->update([
                'name' => $request->name,
                'startdate' => $startdate,
                'enddate' => $enddate,
                'starttime' => $starttime,
                'endtime' => $endtime,
                'duration' => $duration,
                'kkm' => $request->kkm,
                'classid' => $request->classid,
                'amountquestion' => count($request->question_ids),
                'questionmaterialid' => $request->questionmaterialid,
                'trials' => $request->trials,
                'teacher_id' => $user->role === 'admin' ? $request->teacher_id : $user->id,
                'show_score' => $request->has('show_score') ? true : false,
            ]);

            // Hapus dulu semua soal lama (biar gak dobel)
            ExamDetail::where('examid', $exam->id)->delete();

            // Simpan ulang soal-soal baru
            foreach ($request->question_ids as $soalId) {
                ExamDetail::create([
                    'examid' => $exam->id,
                    'questionid' => $soalId,
                    'classid' => $request->classid,
                    'studentid' => null,
                ]);
            }

            return redirect()->route('exam.index')->with('success', 'Data ujian berhasil diperbarui');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
        } catch (\Exception $e) {
            return redirect()->route('exam.index')->with('error', 'Gagal mengubah jadwal ujian: ' . $e->getMessage());
        }
    }


    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $exam = Exam::find($id);

        if ($exam) {
            // Role-based access control: teachers can only delete their own exams
            $user = Auth::user();
            if ($user->role === 'teacher' && $exam->teacher_id != $user->id) {
                return redirect()->route('exam.index')->with('error', 'Anda tidak memiliki akses untuk menghapus ujian ini.');
            }

            $exam->examDetails()->delete(); // kalau relasinya dinamakan examDetails
            $exam->delete();
        }


        return redirect()->route('exam.index')->with('success', 'Jadwal ujian berhasil dihapus');
    }

    public function QuestionTable($examId)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        // Get exam with its details and selected questions
        $exam = Exam::with(['examDetails', 'examDetails.question.answers'])->findOrFail($examId);
        $selectedQuestionIds = $exam->examDetails->pluck('questionid')->toArray();

        // Get all questions for the exam's material
        $questions = Question::with('answers')
            ->where('questionmaterialid', $exam->questionmaterialid)
            ->orderBy('id', 'asc')
            ->get();

        $title = $exam->name;

        // Kirim ke view
        return view('admin.exam.schedule.question', [
            'title' => $title,
            'exam' => $exam,
            'questions' => $questions,
            'pg_questions' => $questions->where('type', 'pilihan_ganda'),
            'urai_questions' => $questions->where('type', 'uraian_singkat'),
            'esai_questions' => $questions->where('type', 'esai'),
            'selectedQuestionIds' => $selectedQuestionIds,
        ]);
    }

    public function student($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $exam = Exam::with(['questionmaterial', 'class.students'])->find($id);

        // Check if exam exists
        if (!$exam) {
            return redirect()->route('exam.index')->with('error', 'Ujian tidak ditemukan');
        }

        // Check if exam has class
        if (!$exam->class) {
            return redirect()->route('exam.index')->with('error', 'Kelas ujian tidak ditemukan');
        }

        // Get students with their exam results and progress
        $students = $exam->class->students->map(function ($student) use ($exam) {
            // Get all exam results for this student
            $examResults = ExamResult::where('examid', $exam->id)
                ->where('studentid', $student->id)
                ->orderBy('score', 'desc')
                ->get();

            // Get best score and attempt count
            $bestScore = $examResults->first()?->score ?? null;
            $attemptCount = $examResults->count();

            // Calculate progress from temporary answers
            $totalQuestions = Question::where('questionmaterialid', $exam->questionmaterialid)->count();

            $answeredQuestions = ExamTemporaryAnswer::where('examid', $exam->id)
                ->where('studentid', $student->id)
                ->where('attempt', $attemptCount + 1) // Current attempt
                ->distinct('questionid')
                ->count();

            $progress = $totalQuestions > 0 ? round(($answeredQuestions / $totalQuestions) * 100) : 0;

            // Add calculated data to student object
            $student->best_score = $bestScore;
            $student->attempt_count = $attemptCount;
            $student->progress = $progress;
            $student->total_questions = $totalQuestions;
            $student->answered_questions = $answeredQuestions;

            return $student;
        });

        return view('admin.exam.schedule.student', [
            'title' => 'Daftar Peserta Ujian',
            'exam' => $exam,
            'students' => $students,
        ]);
    }

    public function getSoalByMaterial($materialId)
    {
        try {
            $questions = Question::with('answers')
                ->where('questionmaterialid', $materialId)
                ->orderBy('id', 'asc')
                ->get();

            $groupedQuestions = $questions->groupBy('type');

            return response()->json($groupedQuestions);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to load questions'], 500);
        }
    }

    public function getQuestion($id, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questions = Question::with('answers')
            ->where('questionmaterialid', $id)
            ->where('type', $type)
            ->orderBy('id', 'asc')
            ->get();

        if ($type === 'pilihan_ganda') {
            return view('admin.exam.schedule.components.pilihan_ganda', compact('questions'));
        } elseif ($type === 'uraian_singkat') {
            return view('admin.exam.schedule.components.uraian_singkat', compact('questions'));
        } elseif ($type === 'esai') {
            return view('admin.exam.schedule.components.esai', compact('questions'));
        } else {
            abort(404, 'Tipe soal tidak dikenali.');
        }
    }

    /**
     * Generate unique token for exam (3 characters: letters and numbers)
     */
    private function generateToken()
    {
        do {
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            $token = '';
            for ($i = 0; $i < 3; $i++) {
                $token .= $characters[random_int(0, strlen($characters) - 1)];
            }
        } while (Exam::where('token', $token)->exists());

        return $token;
    }

    /**
     * Show student exam detail with answers
     */
    public function studentDetail($examId, $studentId)
    {
        try {
            $exam = Exam::with(['questionmaterial', 'class'])->findOrFail($examId);
            $student = Student::findOrFail($studentId);

            // Get student's exam result
            $examResult = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->first();

            if (!$examResult) {
                return redirect()->back()->with('error', 'Hasil ujian tidak ditemukan');
            }

            // Get questions for this exam in original order from exam_details
            $examDetails = ExamDetail::where('examid', $examId)
                ->with(['question.answers'])
                ->orderBy('id', 'asc') // Maintain original order
                ->get();

            // Parse student answers from JSON
            $studentAnswers = $examResult->answers ?? [];

            // Process student answers to decode image_paths if needed
            foreach ($studentAnswers as &$studentAnswer) {
                // Decode image_paths if it's a JSON string
                if (isset($studentAnswer['image_paths']) && is_string($studentAnswer['image_paths'])) {
                    $studentAnswer['image_paths'] = json_decode($studentAnswer['image_paths'], true) ?? [];
                }
            }

            // Group questions by type for better organization, maintaining original order
            $questionsByType = collect();
            foreach ($examDetails as $detail) {
                if ($detail->question) {
                    $type = $detail->question->type;
                    if (!$questionsByType->has($type)) {
                        $questionsByType->put($type, collect());
                    }
                    $questionsByType->get($type)->push($detail->question);
                }
            }

            // Calculate maximum possible score
            $maxScore = $this->calculateMaxScore($examId);

            return view('admin.exam.schedule.student-detail', [
                'title' => 'Detail Jawaban Siswa',
                'exam' => $exam,
                'student' => $student,
                'examResult' => $examResult,
                'questionsByType' => $questionsByType,
                'studentAnswers' => $studentAnswers,
                'maxScore' => $maxScore,
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Update essay question score
     */
    public function updateEssayScore(Request $request, $examId, $studentId)
    {
        try {
            $request->validate([
                'question_id' => 'required|integer',
                'score' => 'required|numeric|min:0',
            ]);

            // Get the question to check maximum score
            $question = Question::with('answers')->find($request->question_id);
            if (!$question || $question->type !== 'esai') {
                return response()->json([
                    'success' => false,
                    'message' => 'Soal esai tidak ditemukan'
                ], 404);
            }

            // Get maximum score for this essay question
            $essayAnswer = $question->answers->where('is_correct', 1)->first();
            $maxScore = $essayAnswer->score ?? 100;

            // Validate score against maximum
            if ($request->score > $maxScore) {
                return response()->json([
                    'success' => false,
                    'message' => "Nilai tidak boleh lebih dari {$maxScore} poin"
                ], 422);
            }

            $examResult = ExamResult::where('examid', $examId)
                ->where('studentid', $studentId)
                ->orderBy('score', 'desc')
                ->first();

            if (!$examResult) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hasil ujian tidak ditemukan'
                ], 404);
            }

            // Update the score for specific essay question
            $answers = $examResult->answers ?? [];

            // Find and update the specific answer
            foreach ($answers as &$answer) {
                if ($answer['question_id'] == $request->question_id && $answer['question_type'] == 'esai') {
                    $answer['essay_score'] = $request->score;
                    break;
                }
            }

            // Recalculate total score
            $totalScore = $this->calculateTotalScore($examId, $answers);

            // Update exam result
            $examResult->update([
                'answers' => $answers,
                'score' => $totalScore,
                'status' => 'graded'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Nilai berhasil disimpan',
                'new_total_score' => $totalScore
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate maximum possible score for an exam
     */
    private function calculateMaxScore($examId)
    {
        // Get exam to determine total possible score
        $exam = Exam::with('questionmaterial')->find($examId);
        if (!$exam) {
            return 0;
        }

        // Get all questions for this exam
        $questions = Question::where('questionmaterialid', $exam->questionmaterialid)->orderBy('id', 'asc')->get();
        $questionsByType = $questions->groupBy('type');

        // Calculate score based on question types
        $multipleChoiceCount = $questionsByType->get('pilihan_ganda', collect())->count();
        $shortAnswerCount = $questionsByType->get('uraian_singkat', collect())->count();

        // Get score settings from question material
        $pgTotalScore = $exam->questionmaterial->pg_total_score ?? ($multipleChoiceCount * 10);
        $uraianTotalScore = $exam->questionmaterial->uraian_total_score ?? ($shortAnswerCount * 15);

        // Calculate essay total score from individual question scores
        $esaiTotalScore = 0;
        $esaiQuestions = $questionsByType->get('esai', collect());
        foreach ($esaiQuestions as $question) {
            $essayAnswer = $question->answers->where('is_correct', 1)->first();
            $esaiTotalScore += $essayAnswer->score ?? 100;
        }

        return round($pgTotalScore + $uraianTotalScore + $esaiTotalScore, 2);
    }

    /**
     * Calculate total score including essay scores
     */
    private function calculateTotalScore($examId, $answers)
    {
        $totalScore = 0;

        // Get exam to determine total possible score
        $exam = Exam::with('questionmaterial')->find($examId);
        if (!$exam) {
            return 0;
        }

        // Get all questions for this exam
        $questions = Question::where('questionmaterialid', $exam->questionmaterialid)->orderBy('id', 'asc')->get();
        $questionsByType = $questions->groupBy('type');

        // Calculate score based on question types
        $multipleChoiceCount = $questionsByType->get('pilihan_ganda', collect())->count();
        $shortAnswerCount = $questionsByType->get('uraian_singkat', collect())->count();

        // Get score settings from question material
        $pgTotalScore = $exam->questionmaterial->pg_total_score ?? ($multipleChoiceCount * 10);
        $uraianTotalScore = $exam->questionmaterial->uraian_total_score ?? ($shortAnswerCount * 15);

        // Calculate score per question (total skor : total soal)
        $scorePerMultipleChoice = $multipleChoiceCount > 0 ? $pgTotalScore / $multipleChoiceCount : 0;
        $scorePerShortAnswer = $shortAnswerCount > 0 ? $uraianTotalScore / $shortAnswerCount : 0;

        foreach ($answers as $answer) {
            if ($answer['question_type'] === 'pilihan_ganda') {
                // Get the question and check if answer is correct
                $question = Question::find($answer['question_id']);
                if ($question) {
                    $correctAnswer = Answer::where('questionid', $question->id)
                        ->where('is_correct', 1)
                        ->first();
                    if ($correctAnswer && $correctAnswer->id == $answer['selected_answer']) {
                        $totalScore += $scorePerMultipleChoice;
                    }
                }
            } elseif ($answer['question_type'] === 'uraian_singkat') {
                // For short answer, check if answer is correct
                $question = Question::find($answer['question_id']);
                if ($question) {
                    $correctAnswer = Answer::where('questionid', $question->id)
                        ->where('is_correct', 1)
                        ->first();

                    if ($correctAnswer && isset($answer['text_answer'])) {
                        // Normalize both answers for comparison (case-insensitive, trim whitespace)
                        $studentAnswer = trim(strtolower($answer['text_answer']));
                        $correctAnswerText = trim(strtolower($correctAnswer->answer));

                        // Check if answers match exactly
                        if ($studentAnswer === $correctAnswerText) {
                            $totalScore += $scorePerShortAnswer;
                        }
                        // If incorrect, no score is added (0 points)
                    }
                }
            } elseif ($answer['question_type'] === 'esai') {
                // Use manually graded score directly (no conversion needed)
                $essayScore = $answer['essay_score'] ?? 0;
                $totalScore += $essayScore;
            }
        }

        return round($totalScore, 2);
    }
}
