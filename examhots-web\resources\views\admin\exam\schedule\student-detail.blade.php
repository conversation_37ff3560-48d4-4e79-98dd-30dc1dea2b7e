@extends('main')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/management-style.css') }}">
@endsection

@section('content')
    <!-- Page Header -->
    <div class="page-header flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
            <div class="bg-primary-blue p-3 rounded-xl">
                <iconify-icon icon="mdi:account-details" class="text-white text-2xl"></iconify-icon>
            </div>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
                <p class="text-gray-600 mt-1">{{ $exam->name }} - {{ $student->name }}</p>
            </div>
        </div>
        <a href="{{ route('exam.student', $exam->id) }}"
            class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center space-x-2">
            <iconify-icon icon="mdi:arrow-left"></iconify-icon>
            <span>Kembali</span>
        </a>
    </div>

    <!-- Student Info Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="text-sm font-medium text-gray-500">Nama Siswa</label>
                <p class="text-gray-900 font-medium">{{ $student->name }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">NISN</label>
                <p class="text-gray-900">{{ $student->nisn }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Kelas</label>
                <p class="text-gray-900">{{ $exam->class->name ?? '-' }}</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-200">
            <div>
                <label class="text-sm font-medium text-gray-500">Skor</label>
                <p class="text-2xl font-bold text-blue-600" id="total-score">{{ $examResult->score }}/{{ $maxScore }}
                </p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Status</label>
                <span
                    class="inline-flex px-2 py-1 text-xs font-medium rounded-full
                        {{ $examResult->status === 'graded' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                    {{ $examResult->status === 'graded' ? 'Sudah Dikoreksi' : 'Belum Dikoreksi' }}
                </span>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Percobaan</label>
                <p class="text-gray-900">{{ $examResult->attempt }}</p>
            </div>
            <div>
                <label class="text-sm font-medium text-gray-500">Waktu Submit</label>
                <p class="text-gray-900">{{ \Carbon\Carbon::parse($examResult->submitted_at)->format('d/m/Y H:i') }}
                </p>
            </div>
        </div>
    </div>

    <!-- Questions and Answers -->
    @foreach ($questionsByType as $type => $questions)
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">
                    @if ($type === 'pilihan_ganda')
                        Soal Pilihan Ganda
                    @elseif($type === 'uraian_singkat')
                        Soal Uraian Singkat
                    @elseif($type === 'esai')
                        Soal Esai
                    @endif
                    <span class="text-sm font-normal text-gray-500">({{ $questions->count() }} soal)</span>
                </h2>
            </div>

            <div class="p-6">
                @foreach ($questions as $index => $question)
                    @php
                        $studentAnswer = collect($studentAnswers)->firstWhere('question_id', $question->id);
                    @endphp

                    <div class="mb-8 pb-6 {{ !$loop->last ? 'border-b border-gray-100' : '' }}">
                        <!-- Question -->
                        <div class="mb-4">
                            <h3 class="font-medium text-gray-900 mb-2">
                                <span class="mr-2">{{ $loop->iteration }}.</span>
                                <div class="question-content inline">{!! safeHtml($question->question) !!}</div>
                            </h3>

                            @if ($question->img)
                                @php
                                    $images = explode(',', $question->img);
                                @endphp
                                <div class="mb-3">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">
                                        Gambar Soal @if (count($images) > 1)
                                            ({{ count($images) }})
                                        @endif:
                                    </label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        @foreach ($images as $image)
                                            @if (trim($image))
                                                <img src="{{ asset('storage/uploads/images/question/' . trim($image)) }}"
                                                    alt="Question Image {{ $loop->iteration }}"
                                                    class="w-full max-w-sm h-48 object-contain rounded-lg border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity bg-gray-50"
                                                    onclick="showImageModal('{{ asset('storage/uploads/images/question/' . trim($image)) }}', 'Gambar Soal {{ $loop->iteration }}')"
                                                    onerror="this.src='{{ asset('storage/uploads/images/placeholder.jpg') }}'; this.onerror=null;">
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Answer Section -->
                        @if ($type === 'pilihan_ganda')
                            <div class="space-y-2">
                                @foreach ($question->answers as $answer)
                                    @php
                                        $isSelected =
                                            $studentAnswer && $studentAnswer['selected_answer'] == $answer->id;
                                        $isCorrect = $answer->is_correct;
                                    @endphp

                                    <div
                                        class="flex items-center p-3 rounded-lg border
                                            {{ $isSelected && $isCorrect
                                                ? 'bg-green-50 border-green-200'
                                                : ($isSelected && !$isCorrect
                                                    ? 'bg-red-50 border-red-200'
                                                    : ($isCorrect
                                                        ? 'bg-blue-50 border-blue-200'
                                                        : 'bg-gray-50 border-gray-200')) }}">

                                        <div class="flex items-center mr-3">
                                            @if ($isSelected)
                                                <iconify-icon
                                                    icon="{{ $isCorrect ? 'mdi:check-circle' : 'mdi:close-circle' }}"
                                                    class="text-lg {{ $isCorrect ? 'text-green-600' : 'text-red-600' }}"></iconify-icon>
                                            @elseif($isCorrect)
                                                <iconify-icon icon="mdi:check-circle-outline"
                                                    class="text-lg text-blue-600"></iconify-icon>
                                            @else
                                                <span class="w-5 h-5 rounded-full border-2 border-gray-300"></span>
                                            @endif
                                        </div>

                                        <div class="answer-content text-gray-900">{!! safeHtml($answer->answer) !!}</div>

                                        @if ($isSelected)
                                            <span
                                                class="ml-auto text-xs font-medium
                                                    {{ $isCorrect ? 'text-green-600' : 'text-red-600' }}">
                                                {{ $isCorrect ? 'Jawaban Siswa (Benar)' : 'Jawaban Siswa (Salah)' }}
                                            </span>
                                        @elseif($isCorrect)
                                            <span class="ml-auto text-xs font-medium text-blue-600">
                                                Jawaban Benar
                                            </span>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @elseif($type === 'uraian_singkat')
                            @php
                                $correctAnswer = $question->answers->where('is_correct', 1)->first();
                                $studentAnswerText = $studentAnswer['text_answer'] ?? '';
                                $isCorrect = false;

                                if ($correctAnswer && !empty($studentAnswerText)) {
                                    $studentAnswerNormalized = trim(strtolower($studentAnswerText));
                                    $correctAnswerNormalized = trim(strtolower($correctAnswer->answer));
                                    $isCorrect = $studentAnswerNormalized === $correctAnswerNormalized;
                                }
                            @endphp

                            <div class="space-y-4">
                                <!-- Correct Answer -->
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-blue-700 mb-2 block">Jawaban yang Benar:</label>
                                    <div class="answer-content text-blue-900">{!! safeHtml($correctAnswer->answer ?? 'Tidak ada jawaban yang benar') !!}
                                    </div>
                                </div>

                                <!-- Student Answer -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Jawaban Siswa:</label>
                                    <div class="flex items-start justify-between">
                                        <div class="answer-content text-gray-900 flex-1">{!! safeHtml($studentAnswerText ?: 'Tidak dijawab') !!}</div>
                                        @if (!empty($studentAnswerText))
                                            <span
                                                class="ml-4 text-xs font-medium px-2 py-1 rounded-full {{ $isCorrect ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700' }}">
                                                {{ $isCorrect ? 'Benar' : 'Salah' }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @elseif($type === 'esai')
                            <div class="space-y-4">
                                <!-- Student Answer -->
                                <div class="bg-gray-50 rounded-lg p-4">
                                    @php
                                        $hasTextAnswer = !empty($studentAnswer['text_answer']);
                                        $hasImageAnswer =
                                            (isset($studentAnswer['image_path']) &&
                                                !empty($studentAnswer['image_path'])) ||
                                            (isset($studentAnswer['image_paths']) &&
                                                !empty($studentAnswer['image_paths']));
                                    @endphp

                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Jawaban Siswa:</label>
                                    @if ($hasTextAnswer)
                                        <div class="answer-content text-gray-900 whitespace-pre-wrap">
                                            {!! safeHtml($studentAnswer['text_answer']) !!}
                                        </div>
                                    @elseif (!$hasImageAnswer)
                                        <p class="text-gray-500 italic">
                                            Tidak dijawab
                                        </p>
                                    @endif

                                    @if ($hasImageAnswer)
                                        <div class="{{ $hasTextAnswer ? 'mt-3' : '' }}">
                                            @php
                                                // Handle multiple images first (new format)
                                                $imagePaths = [];
                                                if (
                                                    isset($studentAnswer['image_paths']) &&
                                                    !empty($studentAnswer['image_paths'])
                                                ) {
                                                    $imagePaths = is_array($studentAnswer['image_paths'])
                                                        ? $studentAnswer['image_paths']
                                                        : json_decode($studentAnswer['image_paths'], true) ?? [];
                                                } elseif (
                                                    isset($studentAnswer['image_path']) &&
                                                    !empty($studentAnswer['image_path'])
                                                ) {
                                                    // Fallback to single image (backward compatibility)
                                                    $imagePaths = [$studentAnswer['image_path']];
                                                }
                                            @endphp

                                            {{-- Always show label for images --}}
                                            <label class="text-sm font-medium text-gray-700 mb-2 block">
                                                Gambar Jawaban @if (count($imagePaths) > 1)
                                                    ({{ count($imagePaths) }})
                                                @endif:
                                            </label>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                @foreach ($imagePaths as $imagePath)
                                                    @php
                                                        if (strpos($imagePath, '/') === false) {
                                                            // It's just a filename, use answers directory
    $imageUrl = asset(
        'storage/uploads/images/answers/' . $imagePath,
    );
} else {
    // It's a full path (legacy), show placeholder
                                                            $imageUrl = asset('storage/uploads/images/placeholder.jpg');
                                                        }
                                                    @endphp
                                                    <img src="{{ $imageUrl }}"
                                                        alt="Answer Image {{ $loop->iteration }}"
                                                        class="w-full max-w-sm h-48 object-contain rounded-lg border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity bg-gray-50"
                                                        onclick="showImageModal('{{ $imageUrl }}', 'Gambar Jawaban {{ $loop->iteration }}')"
                                                        onerror="this.src='{{ asset('storage/uploads/images/placeholder.jpg') }}'; this.onerror=null;">
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Essay Scoring -->
                                @php
                                    $essayAnswer = $question->answers->where('is_correct', 1)->first();
                                    $maxScore = $essayAnswer->score ?? 100;
                                    $currentScore = $studentAnswer['essay_score'] ?? 0;
                                @endphp
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <label class="text-sm font-medium text-gray-700 mb-2 block">Penilaian Guru:</label>
                                    <div class="flex items-center space-x-3">
                                        <input type="number" id="essay-score-{{ $question->id }}"
                                            value="{{ $currentScore }}" min="0" max="{{ $maxScore }}"
                                            data-max-score="{{ $maxScore }}"
                                            class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <span class="text-gray-600">/ {{ $maxScore }}</span>
                                        <button onclick="updateEssayScore({{ $question->id }})"
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                            Simpan Nilai
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Skor maksimal: {{ $maxScore }} poin</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach
    </div>

    <script>
        function updateEssayScore(questionId) {
            const scoreInput = document.getElementById(`essay-score-${questionId}`);
            const score = parseFloat(scoreInput.value) || 0;
            const maxScore = parseFloat(scoreInput.getAttribute('data-max-score')) || 100;

            if (score < 0 || score > maxScore) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: `Nilai harus antara 0 dan ${maxScore}`,
                });
                return;
            }

            // Show loading
            Swal.fire({
                title: 'Menyimpan...',
                text: 'Sedang menyimpan nilai',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/exam/{{ $exam->id }}/student/{{ $student->id }}/essay-score`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        question_id: questionId,
                        score: score
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update total score display
                        document.getElementById('total-score').textContent = data.new_total_score +
                            '/{{ $maxScore }}';

                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: data.message,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: data.message,
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat menyimpan nilai',
                    });
                });
        }

        // Image modal functionality
        function showImageModal(imageUrl, title) {
            Swal.fire({
                title: `<div class="text-lg font-semibold text-gray-800">${title || 'Gambar'}</div>`,
                html: `
                    <div class="image-container">
                        <img src="${imageUrl}"
                             alt="${title || 'Image'}"
                             class="modal-image"
                             onerror="this.src='{{ asset('storage/uploads/images/placeholder.jpg') }}'; this.onerror=null;">
                        <div class="image-controls">
                            <button type="button" class="control-btn" onclick="downloadImage('${imageUrl}', '${title || 'image'}')">
                                <iconify-icon icon="mdi:download" class="mr-2"></iconify-icon>
                                <span>Download</span>
                            </button>
                            <button type="button" class="control-btn" onclick="Swal.close()">
                                <iconify-icon icon="mdi:close" class="mr-2"></iconify-icon>
                                <span>Tutup</span>
                            </button>
                        </div>
                    </div>
                `,
                showCloseButton: false,
                showConfirmButton: false,
                customClass: {
                    popup: 'image-modal-popup',
                    htmlContainer: 'image-modal-container'
                },
                width: '90%',
                padding: '1rem',
                background: '#ffffff',
                backdrop: 'rgba(0, 0, 0, 0.8)',
                didOpen: () => {
                    // Add click to close functionality on image
                    const image = document.querySelector('.modal-image');
                    if (image) {
                        image.style.cursor = 'zoom-out';
                        image.onclick = () => Swal.close();
                    }

                    // Add keyboard navigation
                    document.addEventListener('keydown', handleImageModalKeydown);
                },
                willClose: () => {
                    document.removeEventListener('keydown', handleImageModalKeydown);
                }
            });
        }

        // Handle keyboard navigation in image modal
        function handleImageModalKeydown(event) {
            if (event.key === 'Escape') {
                Swal.close();
            }
        }

        // Download image function
        function downloadImage(imageUrl, filename) {
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = filename.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '.jpg';
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>

    <style>
        .image-modal-popup {
            max-width: 95vw !important;
            max-height: 95vh !important;
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
        }

        .image-modal-container {
            padding: 0 !important;
            margin: 0 !important;
        }

        .image-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .modal-image {
            max-width: 100% !important;
            max-height: 80vh !important;
            object-fit: contain !important;
            border-radius: 8px 8px 0 0 !important;
            background-color: #f9fafb;
        }

        .image-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 1rem;
            background-color: #f9fafb;
            border-top: 1px solid #e5e7eb;
        }

        .control-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            margin: 0 0.5rem;
            background-color: #455A9D;
            color: white;
            border: none;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .control-btn:hover {
            background-color: #31406F;
        }

        .control-btn iconify-icon {
            margin-right: 0.5rem;
        }
    </style>
@endsection
